export type MessageType = "text" | "file" | "image" | "sticker";
export type MessageStatus = "sending" | "delivered" | "read" | "failed";

export interface ChatMessage {
  id: number;
  chatId: number;
  userId: number;
  content: string;
  messageType: MessageType;
  messageStatus: MessageStatus;
  createdAt: Date;
  updatedAt: Date;
}

export interface CreateChatMessageData {
  chatId: number;
  userId: number;
  content: string;
  messageType?: MessageType;
  messageStatus?: MessageStatus;
}

export interface UpdateChatMessageData {
  content?: string;
  messageType?: MessageType;
  messageStatus?: MessageStatus;
}

export interface ChatMessageFilters {
  chatId?: number;
  userId?: number;
  messageType?: MessageType;
  messageStatus?: MessageStatus;
  limit?: number;
  offset?: number;
}
