import { ChatMessageService } from '../services';
import { MessageType, MessageStatus } from '../types';

/**
 * Example usage of ChatMessageService
 * This file demonstrates how to use the ChatMessage service methods
 */

export async function chatMessageExamples() {
  try {
    // 1. Create a new chat message
    console.log('📝 Creating a new chat message...');
    const newMessage = await ChatMessageService.create({
      chatId: 1,
      userId: 123,
      content: 'Hello, this is a test message!',
      messageType: MessageType.TEXT,
      messageStatus: MessageStatus.SENDING
    });
    console.log('✅ Created message:', newMessage);

    // 2. Get a message by ID
    console.log('\n🔍 Fetching message by ID...');
    const fetchedMessage = await ChatMessageService.getById(newMessage.id);
    console.log('✅ Fetched message:', fetchedMessage);

    // 3. Update message content
    console.log('\n✏️ Updating message content...');
    const updatedMessage = await ChatMessageService.update(newMessage.id, {
      content: 'Updated message content!',
      messageStatus: MessageStatus.DELIVERED
    });
    console.log('✅ Updated message:', updatedMessage);

    // 4. Get messages by chat ID
    console.log('\n📋 Getting messages for chat...');
    const chatMessages = await ChatMessageService.getByChatId(1, 10, 0);
    console.log('✅ Chat messages:', chatMessages);

    // 5. Mark message as read
    console.log('\n👁️ Marking message as read...');
    const readMessage = await ChatMessageService.markAsRead(newMessage.id);
    console.log('✅ Message marked as read:', readMessage);

    // 6. Count messages in chat
    console.log('\n🔢 Counting messages in chat...');
    const messageCount = await ChatMessageService.count({ chatId: 1 });
    console.log('✅ Total messages in chat:', messageCount);

    // 7. Get messages with filters
    console.log('\n🔍 Getting filtered messages...');
    const filteredMessages = await ChatMessageService.getMany({
      chatId: 1,
      messageType: MessageType.TEXT,
      messageStatus: MessageStatus.READ,
      limit: 5,
      offset: 0
    });
    console.log('✅ Filtered messages:', filteredMessages);

  } catch (error) {
    console.error('❌ Error in chat message examples:', error);
  }
}

// Export individual example functions for testing specific operations
export async function createMessageExample() {
  return await ChatMessageService.create({
    chatId: 1,
    userId: 123,
    content: 'Test message',
    messageType: MessageType.TEXT
  });
}

export async function getMessagesExample(chatId: number) {
  return await ChatMessageService.getByChatId(chatId, 20, 0);
}

export async function updateMessageExample(messageId: number, content: string) {
  return await ChatMessageService.update(messageId, { content });
}

export async function markMessageReadExample(messageId: number) {
  return await ChatMessageService.markAsRead(messageId);
}
