import {
  createChatMessage,
  getChatMessageById,
  getManyChatMessages,
  getChatMessagesByChatId,
  updateChatMessage,
  markChatMessageAsRead,
  countChatMessages
} from '../services';

/**
 * Example usage of ChatMessageService
 * This file demonstrates how to use the ChatMessage service methods
 */

export async function chatMessageExamples() {
  try {
    // 1. Create a new chat message
    console.log('📝 Creating a new chat message...');
    const newMessage = await createChatMessage({
      chatId: 1,
      userId: 123,
      content: 'Hello, this is a test message!',
      messageType: "text",
      messageStatus: "sending"
    });
    console.log('✅ Created message:', newMessage);

    // 1.1. Create a link message
    console.log('📝 Creating a link message...');
    const linkMessage = await createChatMessage({
      chatId: 1,
      userId: 123,
      content: 'https://example.com/shared-link',
      messageType: "link",
      messageStatus: "sending"
    });
    console.log('✅ Created link message:', linkMessage);

    // 2. Get a message by ID
    console.log('\n🔍 Fetching message by ID...');
    const fetchedMessage = await getChatMessageById(newMessage.id);
    console.log('✅ Fetched message:', fetchedMessage);

    // 3. Update message content
    console.log('\n✏️ Updating message content...');
    const updatedMessage = await updateChatMessage(newMessage.id, {
      content: 'Updated message content!',
      messageStatus: "delivered"
    });
    console.log('✅ Updated message:', updatedMessage);

    // 4. Get messages by chat ID
    console.log('\n📋 Getting messages for chat...');
    const chatMessages = await getChatMessagesByChatId(1, 10, 0);
    console.log('✅ Chat messages:', chatMessages);

    // 5. Mark message as read
    console.log('\n👁️ Marking message as read...');
    const readMessage = await markChatMessageAsRead(newMessage.id);
    console.log('✅ Message marked as read:', readMessage);

    // 6. Count messages in chat
    console.log('\n🔢 Counting messages in chat...');
    const messageCount = await countChatMessages({ chatId: 1 });
    console.log('✅ Total messages in chat:', messageCount);

    // 7. Get messages with filters
    console.log('\n🔍 Getting filtered messages...');
    const filteredMessages = await getManyChatMessages({
      chatId: 1,
      messageType: "text",
      messageStatus: "read",
      limit: 5,
      offset: 0
    });
    console.log('✅ Filtered messages:', filteredMessages);

    // 8. Get link messages specifically
    console.log('\n🔗 Getting link messages...');
    const linkMessages = await getManyChatMessages({
      chatId: 1,
      messageType: "link",
      limit: 10,
      offset: 0
    });
    console.log('✅ Link messages:', linkMessages);

  } catch (error) {
    console.error('❌ Error in chat message examples:', error);
  }
}

// Export individual example functions for testing specific operations
export async function createMessageExample() {
  return await createChatMessage({
    chatId: 1,
    userId: 123,
    content: 'Test message',
    messageType: "text"
  });
}

export async function createLinkMessageExample() {
  return await createChatMessage({
    chatId: 1,
    userId: 123,
    content: 'https://example.com/shared-link',
    messageType: "link"
  });
}

export async function getMessagesExample(chatId: number) {
  return await getChatMessagesByChatId(chatId, 20, 0);
}

export async function updateMessageExample(messageId: number, content: string) {
  return await updateChatMessage(messageId, { content });
}

export async function markMessageReadExample(messageId: number) {
  return await markChatMessageAsRead(messageId);
}
